import { DBOS, WorkflowQueue } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './core/ConversationService';
import { PromptService } from './core/PromptService';
import { ConversationDecayService } from './core/ConversationDecayService';
import { SessionService } from './core/SessionService';
import { SessionCleanupService } from './core/SessionCleanupService';
import { CharacterWorkflowService } from './core/CharacterWorkflowService';
import { ConversationWorkflowService } from './core/ConversationWorkflowService';
import { MessageQueueService } from './core/MessageQueueService';
import { LLMService } from './services/LLMService';
import { Conversation, Message, DelayedMessage, Session, SessionCreateRequest } from './models/types';
import { logger } from './utils/Logger';

// Re-export character queue for backward compatibility
export const characterQueue = CharacterWorkflowService.getCharacterQueue();

// Legacy ForaChat class for backward compatibility
// New code should use ChatService instead
export class ForaChat {
    // Static LLM service instance for dependency injection
    private static llmService: LLMService | null = null;

    // Method to set LLM service for testing
    static setLLMService(service: LLMService): void {
        ForaChat.llmService = service;
        // Also set it for the workflow services
        CharacterWorkflowService.setLLMService(service);
        ConversationWorkflowService.setLLMService(service);
    }



    static async createConversation(): Promise<Conversation> {
        return ConversationService.createConversation();
    }

    static async addMessage(character: string, text: string, conversation_id: number): Promise<Message> {
        return ConversationService.addMessage(character, text, conversation_id);
    }

    static async getSystemPrompt(): Promise<string> {
        return PromptService.getSystemPrompt();
    }

    @DBOS.transaction()
    static async getMessageCount(conversationId: number): Promise<number> {
        return ConversationWorkflowService.getMessageCount(conversationId);
    }

    @DBOS.transaction()
    static async updateConversationMetadata(
        conversationId: number,
        theme?: string,
        skills?: string[]
    ): Promise<void> {
        return ConversationWorkflowService.updateConversationMetadata(conversationId, theme, skills);
    }

    static async getDelayedThoughts(conversationId: number, lastMessageId?: number): Promise<DelayedMessage[]> {
        return ConversationService.getDelayedThoughts(conversationId, lastMessageId);
    }

    static async getConversation(conversationId: number): Promise<Conversation | null> {
        return ConversationService.getConversation(conversationId);
    }

    @DBOS.workflow()
    static async getConversationMessages(conversationId: number): Promise<Message[]> {
        return ConversationService.getConversationMessages(conversationId);
    }

    @DBOS.workflow()
    static async getQueuedMessages(conversationId: number): Promise<any[]> {
        return MessageQueueService.getPendingMessages(conversationId);
    }

    @DBOS.workflow()
    static async updateConversationEngagement(conversationId: number): Promise<{ engagementLevel: number; shouldTimeout: boolean; delayMultiplier: number }> {
        return ConversationDecayService.updateConversationEngagement(conversationId);
    }

    @DBOS.workflow()
    static async getLastUserActivity(conversationId: number): Promise<Date | null> {
        return ConversationService.getLastUserActivity(conversationId);
    }

    // Session Management Operations
    @DBOS.workflow()
    static async createSession(request: SessionCreateRequest): Promise<Session> {
        return SessionService.createSession(request);
    }

    @DBOS.workflow()
    static async getSession(sessionId: string): Promise<Session | null> {
        return SessionService.getSession(sessionId);
    }

    @DBOS.workflow()
    static async getSessionByUserAndChannel(userIdentifier: string, channel: string): Promise<Session | null> {
        return SessionService.getSessionByUserAndChannel(userIdentifier, channel);
    }

    @DBOS.workflow()
    static async updateSessionActivity(sessionId: string): Promise<void> {
        return SessionService.updateSessionActivity(sessionId);
    }

    @DBOS.workflow()
    static async updateSessionConversation(sessionId: string, conversationId: number): Promise<void> {
        return SessionService.updateSessionConversation(sessionId, conversationId);
    }

    @DBOS.workflow()
    static async deleteSession(sessionId: string): Promise<void> {
        return SessionService.deleteSession(sessionId);
    }

    @DBOS.workflow()
    static async cleanupExpiredSessions(): Promise<number> {
        return SessionService.cleanupExpiredSessions();
    }

    @DBOS.workflow()
    static async extendSession(sessionId: string, additionalHours: number = 24): Promise<void> {
        return SessionService.extendSession(sessionId, additionalHours);
    }

    // Session-Conversation Integration Operations
    @DBOS.workflow()
    static async createConversationForSession(sessionId: string): Promise<Conversation> {
        return ConversationService.createConversationForSession(sessionId);
    }

    @DBOS.workflow()
    static async getConversationBySession(sessionId: string): Promise<Conversation | null> {
        return ConversationService.getConversationBySession(sessionId);
    }

    @DBOS.workflow()
    static async getSessionsForConversation(conversationId: number): Promise<Session[]> {
        return ConversationService.getSessionsForConversation(conversationId);
    }

    @DBOS.workflow()
    static async getConversationWithSessions(conversationId: number): Promise<{
        conversation: Conversation | null;
        sessions: Session[];
        messages: Message[];
    }> {
        return ConversationService.getConversationWithSessions(conversationId);
    }

    @DBOS.workflow()
    static async transferConversationToSession(
        conversationId: number,
        fromSessionId: string,
        toSessionId: string
    ): Promise<void> {
        return ConversationService.transferConversationToSession(conversationId, fromSessionId, toSessionId);
    }

    @DBOS.workflow()
    static async getActiveSessionsForUser(userIdentifier: string): Promise<Session[]> {
        return ConversationService.getActiveSessionsForUser(userIdentifier);
    }

    // Session Cleanup Operations
    @DBOS.workflow()
    static async performSessionCleanup(): Promise<{
        expiredSessions: number;
        orphanedConversations: number;
        oldMessages: number;
    }> {
        return SessionCleanupService.performCleanup();
    }

    @DBOS.workflow()
    static async getCleanupStats(): Promise<{
        totalSessions: number;
        activeSessions: number;
        expiredSessions: number;
        totalConversations: number;
        orphanedConversations: number;
        totalMessages: number;
        oldMessages: number;
    }> {
        return SessionCleanupService.getCleanupStats();
    }

    @DBOS.workflow()
    static async manualSessionCleanup(options: any = {}): Promise<{
        expiredSessions: number;
        orphanedConversations: number;
        oldMessages: number;
        messageQueueEntries: number;
    }> {
        return SessionCleanupService.manualCleanup(options);
    }



    @DBOS.workflow()
    static async chatWorkflow(userMessage: string, conversationId?: number): Promise<any> {
        const result = await ConversationWorkflowService.chatWorkflow(userMessage, conversationId);

        // Queue character thoughts if they were returned
        if (result.characterThoughts) {
            logger.info(`Queueing ${result.characterThoughts.length} character thoughts for conversation ${result.conversationId}`);
            for (const thought of result.characterThoughts) {
                try {
                    logger.info(`Queueing character thought: ${thought.character} with delay ${Math.round(thought.delay)}ms`);
                    DBOS.startWorkflow(ForaChat, {
                        queueName: CharacterWorkflowService.getCharacterQueue().name
                    }).delayedCharacterThoughtWorkflow(result.conversationId, thought.context, thought.character, thought.delay);
                } catch (error) {
                    // In test environment, workflow queuing might fail - log but don't break
                    logger.warn(`Failed to queue character thought for ${thought.character}`, error);
                }
            }
            // Remove characterThoughts from the result before returning
            delete result.characterThoughts;
        } else {
            logger.info(`No character thoughts to queue for conversation ${result.conversationId || 'unknown'}`);
        }

        return result;
    }

    @DBOS.workflow()
    static async interruptedChatWorkflow(
        userMessage: string,
        previousMessages: Array<{character: string, text: string}>,
        conversationId?: number
    ): Promise<any> {
        const result = await ConversationWorkflowService.interruptedChatWorkflow(userMessage, previousMessages, conversationId);

        // Queue character thoughts if they were returned
        if (result.characterThoughts) {
            for (const thought of result.characterThoughts) {
                try {
                    DBOS.startWorkflow(ForaChat, {
                        queueName: CharacterWorkflowService.getCharacterQueue().name
                    }).delayedCharacterThoughtWorkflow(result.conversationId, thought.context, thought.character, thought.delay);
                } catch (error) {
                    // In test environment, workflow queuing might fail - log but don't break
                    logger.warn(`Failed to queue character thought for ${thought.character}`, error);
                }
            }
            // Remove characterThoughts from the result before returning
            delete result.characterThoughts;
        }

        return result;
    }

    @DBOS.transaction()
    static async getConversationRelevanceContext(conversationId: number): Promise<{
        conversation: Conversation | null;
        recentMessages: Message[];
        messageCount: number;
    }> {
        return ConversationWorkflowService.getConversationRelevanceContext(conversationId);
    }

    @DBOS.workflow()
    static async determineConversationRelevance(
        userMessage: string,
        conversationId: number
    ): Promise<boolean> {
        return ConversationWorkflowService.determineConversationRelevance(userMessage, conversationId);
    }

    // Character workflow methods moved to CharacterWorkflowService
    @DBOS.workflow()
    static async delayedCharacterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string,
        initialDelay: number
    ): Promise<any> {
        return CharacterWorkflowService.delayedCharacterThoughtWorkflow(conversationId, context, character, initialDelay);
    }

    @DBOS.workflow()
    static async characterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        return CharacterWorkflowService.characterThoughtWorkflow(conversationId, context, character);
    }

    @DBOS.workflow()
    static async extendedWorkflowThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        return CharacterWorkflowService.extendedWorkflowThoughtWorkflow(conversationId, context, character);
    }


}
